<?php

use App\Http\Controllers\TreePublicController;
use App\Http\Controllers\WelcomeController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::get('', [WelcomeController::class, 'index'])->name('welcome');

require __DIR__ . '/dashboard/auth.php';

Route::view('safety-standard', 'safety-standard');

Route::inertia('privacy-policy', 'PrivacyPolicy');
Route::inertia('refund-policy', 'RefundPolicy');

// Public tree export route (no authentication required)
Route::get('tree/export/{branch}', [TreePublicController::class, 'export'])
    ->name('tree.export.public');

Route::get('records', fn() => response()->file(public_path('.well-known/assetlinks.json')));
