import { AppNode } from '@/store/tree';
import { NodeModel } from '@/types/models';
import { Edge } from '@xyflow/react';
import { create } from 'zustand';

export type BloodlineState = {
  selectedNodeId: string | null;
  highlightedNodeIds: Set<string>;
  highlightedEdgeIds: Set<string>;
};

export type BloodlineActions = {
  selectNode: (nodeId: string | null, nodes: AppNode[], edges: Edge[]) => void;
  clearHighlights: () => void;
  isNodeHighlighted: (nodeId: string) => boolean;
  isEdgeHighlighted: (edgeId: string) => boolean;
};

export type BloodlineStore = BloodlineState & BloodlineActions;

const calculatePathFromRoot = (
  selectedNodeId: string,
  nodes: AppNode[],
  edges: Edge[],
): { nodeIds: string[]; edgeIds: string[] } => {
  console.log('calculatePathFromRoot');
  if (!selectedNodeId || nodes.length === 0) {
    return { nodeIds: [], edgeIds: [] };
  }

  // Create a map for efficient lookups
  const nodeMap = new Map<string, NodeModel>();

  // Build node map
  nodes.forEach((node) => {
    const nodeModel = node.data.nodeModel;
    nodeMap.set(node.id, nodeModel);
  });

  const selectedNode = nodeMap.get(selectedNodeId);
  if (!selectedNode) {
    return { nodeIds: [], edgeIds: [] };
  }

  // Calculate path from root to selected node (ancestors + selected)
  const pathNodeIds: string[] = [selectedNodeId];
  let currentNodeId: string | null = selectedNode.parent_id?.toString() || null;

  while (currentNodeId && nodeMap.has(currentNodeId)) {
    pathNodeIds.unshift(currentNodeId); // Add to beginning to maintain root-to-selected order
    const currentNode = nodeMap.get(currentNodeId)!;
    currentNodeId = currentNode.parent_id?.toString() || null;
  }

  // Calculate edges that connect the path nodes
  const pathEdgeIds: string[] = [];
  for (let i = 0; i < pathNodeIds.length - 1; i++) {
    const sourceId = pathNodeIds[i];
    const targetId = pathNodeIds[i + 1];

    // Find the edge that connects these nodes
    const edge = edges.find((e) => e.source === sourceId && e.target === targetId);
    if (edge) {
      pathEdgeIds.push(edge.id);
    }
  }

  console.log('end calculatePathFromRoot');
  return { nodeIds: pathNodeIds, edgeIds: pathEdgeIds };
};

export const useBloodlineStore = create<BloodlineStore>((set, get) => ({
  selectedNodeId: null,
  highlightedNodeIds: new Set<string>(),
  highlightedEdgeIds: new Set<string>(),

  selectNode: (nodeId, nodes, edges) => {
    if (!nodeId) {
      set({
        selectedNodeId: null,
        highlightedNodeIds: new Set<string>(),
        highlightedEdgeIds: new Set<string>(),
      });
      return;
    }

    // Calculate path from root to selected node
    const { nodeIds, edgeIds } = calculatePathFromRoot(nodeId, nodes, edges);

    // Update store with selected node and highlighted path
    set({
      selectedNodeId: nodeId,
      highlightedNodeIds: new Set(nodeIds),
      highlightedEdgeIds: new Set(edgeIds),
    });
  },

  clearHighlights: () =>
    set({
      selectedNodeId: null,
      highlightedNodeIds: new Set<string>(),
      highlightedEdgeIds: new Set<string>(),
    }),

  isNodeHighlighted: (nodeId) => {
    const { highlightedNodeIds } = get();
    return highlightedNodeIds.has(nodeId);
  },

  isEdgeHighlighted: (edgeId) => {
    const { highlightedEdgeIds } = get();
    return highlightedEdgeIds.has(edgeId);
  },
}));
