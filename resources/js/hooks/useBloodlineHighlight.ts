import { useBloodlineStore } from '@/store/bloodline';
import { useTreeStore } from '@/store/tree';
import { useCallback } from 'react';

export const useBloodlineHighlight = () => {
  const nodes = useTreeStore((state) => state.nodes);
  const edges = useTreeStore((state) => state.edges);
  const { selectNode, clearHighlights, isNodeHighlighted, isEdgeHighlighted } = useBloodlineStore();

  const handleNodeClick = useCallback(
    (nodeId: string | null) => selectNode(nodeId, nodes, edges),
    [selectNode, nodes, edges],
  );

  return {
    handleNodeClick,
    clearHighlights,
    isNodeHighlighted,
    isEdgeHighlighted,
  };
};
