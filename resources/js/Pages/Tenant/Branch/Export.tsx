import { Tree } from '@/components/tree/Tree';
import { NodeModel } from '@/types/models';
import { TreeSettings } from './Show';

type Props = {
  root: NodeModel;
  branch: { name: string; id: number };
  familyName: string;
  settings: TreeSettings;
};

export default function Export({ branch, familyName, settings }: Props) {
  return (
    <div className="min-h-screen bg-white">
      {/* Header for PDF */}
      <div className="border-b-2 border-gray-200 p-6 text-center print:p-4">
        <h1 className="mb-2 text-3xl font-bold text-gray-900 print:text-2xl">{familyName}</h1>
        <p className="text-sm text-gray-600">
          تم التصدير في: {new Date().toLocaleDateString('ar-SA')} - {new Date().toLocaleTimeString('ar-SA')}
        </p>
      </div>

      {/* Tree Container */}
      <div className="relative h-[800px] bg-gray-50 print:h-[600px]">
        <Tree 
          editable={false} 
          showSearch={false}
        />
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          body {
            margin: 0;
            padding: 0;
          }
          
          .react-flow__controls,
          .react-flow__minimap,
          .react-flow__panel {
            display: none !important;
          }
          
          .react-flow__node {
            pointer-events: none;
          }
          
          @page {
            size: A4;
            margin: 10mm;
          }
        }
        
        /* Hide interactive elements for PDF */
        .react-flow__controls,
        .react-flow__minimap {
          display: none !important;
        }
        
        .react-flow__node {
          pointer-events: none;
        }
      `}</style>
    </div>
  );
}
