<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $familyName }} - تصدير الشجرة</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- React Flow CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@xyflow/react@12/dist/base.css">
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background: white;
            margin: 0;
            padding: 20px;
        }
        
        .tree-container {
            width: 100%;
            height: 800px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .family-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .export-date {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        /* Hide interactive elements for PDF */
        .react-flow__controls,
        .react-flow__minimap,
        .react-flow__panel {
            display: none !important;
        }
        
        /* Ensure nodes are visible */
        .react-flow__node {
            pointer-events: none;
        }
        
        /* Print optimizations */
        @media print {
            body {
                margin: 0;
                padding: 10px;
            }
            
            .tree-container {
                height: auto;
                min-height: 600px;
                border: none;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="family-title">{{ $familyName }}</h1>
        <p class="export-date">تم التصدير في: {{ now()->format('Y/m/d H:i') }}</p>
    </div>
    
    <div id="tree-root" class="tree-container"></div>
    
    <!-- React and required dependencies -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@xyflow/react@12/dist/umd/index.js"></script>
    
    <script>
        // Tree data and configuration
        const treeData = {
            branchId: {{ $branch->id }},
            rootId: {{ $root->id }},
            apiUrl: '{{ $tenant->tree_url }}/user/nodes',
        };
        
        // Simple tree rendering component
        const { useState, useEffect } = React;
        const { ReactFlow, Background, BackgroundVariant } = ReactFlowLib;
        
        function TreeExport() {
            const [nodes, setNodes] = useState([]);
            const [edges, setEdges] = useState([]);
            const [loading, setLoading] = useState(true);
            
            useEffect(() => {
                // Fetch tree data
                fetch(treeData.apiUrl, {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        // Process nodes and edges for React Flow
                        const processedNodes = data.nodes.map(node => ({
                            id: node.id.toString(),
                            type: 'default',
                            position: { 
                                x: node.style?.x || 0, 
                                y: node.style?.y || 0 
                            },
                            data: { 
                                label: node.name,
                                nodeModel: node
                            },
                            style: {
                                background: node.bg_color || '#ffffff',
                                border: '2px solid #1f2937',
                                borderRadius: '8px',
                                padding: '10px',
                                fontSize: '12px',
                                fontWeight: '500',
                                color: '#1f2937',
                                minWidth: '120px',
                                textAlign: 'center'
                            }
                        }));
                        
                        // Create edges based on parent-child relationships
                        const processedEdges = data.nodes
                            .filter(node => node.parent_id)
                            .map(node => ({
                                id: `edge-${node.parent_id}-${node.id}`,
                                source: node.parent_id.toString(),
                                target: node.id.toString(),
                                type: 'smoothstep',
                                style: { stroke: '#6b7280', strokeWidth: 2 }
                            }));
                        
                        setNodes(processedNodes);
                        setEdges(processedEdges);
                        setLoading(false);
                    })
                    .catch(error => {
                        console.error('Error fetching tree data:', error);
                        setLoading(false);
                    });
            }, []);
            
            if (loading) {
                return React.createElement('div', {
                    className: 'flex items-center justify-center h-full'
                }, 'جاري تحميل الشجرة...');
            }
            
            return React.createElement(ReactFlow, {
                nodes: nodes,
                edges: edges,
                fitView: true,
                fitViewOptions: { maxZoom: 1, minZoom: 0.5 },
                nodesDraggable: false,
                nodesConnectable: false,
                elementsSelectable: false,
                panOnDrag: false,
                zoomOnScroll: false,
                zoomOnPinch: false,
                zoomOnDoubleClick: false,
                preventScrolling: true
            }, React.createElement(Background, {
                variant: BackgroundVariant.Dots,
                gap: 20,
                size: 1,
                color: '#e5e7eb'
            }));
        }
        
        // Render the tree
        const root = ReactDOM.createRoot(document.getElementById('tree-root'));
        root.render(React.createElement(TreeExport));
    </script>
</body>
</html>
