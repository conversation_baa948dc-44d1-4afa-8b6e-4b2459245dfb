<?php

namespace App\Services;

use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Http;

class CloudflarePdfService
{
    public static function convertUrlToPdf(string $url, string $filename = 'export'): Response
    {
        $accountId = config('services.cloudflare.account_id');
        $apiToken = config('services.cloudflare.api_token');

        if (!$accountId || !$apiToken) {
            throw new Exception('Cloudflare credentials not configured');
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => "Bearer {$apiToken}",
                'Content-Type' => 'application/json',
            ])->post("https://api.cloudflare.com/client/v4/accounts/{$accountId}/browser-rendering/pdf", [
                'url' => $url,
                'options' => [
                    'format' => 'A4',
                    'margin' => [
                        'top' => '20px',
                        'bottom' => '20px',
                        'left' => '20px',
                        'right' => '20px',
                    ],
                    'printBackground' => true,
                    'landscape' => false,
                    'scale' => 1,
                    'waitUntil' => 'networkidle0',
                    'timeout' => 30000,
                ],
            ]);

            if (!$response->successful()) {
                $error = $response->json('errors.0.message') ?? 'Unknown error';
                throw new Exception("Cloudflare PDF generation failed: {$error}");
            }

            $pdfContent = $response->body();

            return response($pdfContent, 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => "attachment; filename=\"{$filename}.pdf\"",
                'Content-Length' => strlen($pdfContent),
            ]);
        } catch (Exception $e) {
            throw new Exception("Failed to convert URL to PDF: " . $e->getMessage());
        }
    }
}
