<?php

namespace App\Services;

use Exception;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Ilovepdf\Ilovepdf;

class ILovePdfService
{
    public static function compressPdf(string $filePath): string
    {
        if (!Storage::exists($filePath)) {
            throw new Exception("File not found: {$filePath}");
        }

        $tempName = Str::random(10);

        $tempFilePath = Storage::disk('local')->path("temp/$tempName.pdf");

        Storage::disk('local')->put("temp/$tempName.pdf", Storage::get($filePath));

        try {
            $compressTask = (new Ilovepdf(
                config('services.ilove_pdf.public_key'),
                config('services.ilove_pdf.secret_key')
            ))->newTask('compress');

            $compressTask->addFile($tempFilePath);
            $compressTask->setCompressionLevel('recommended');
            $compressTask->execute();

            $compressTask->download(Storage::disk('local')->path('temp'));

            return file_get_contents($tempFilePath);
        } finally {
            // Clean up temp file
            if (file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }
        }
    }

    public static function convertHtmlToPdf(string $url, string $filename = 'export'): Response
    {
        $tempDir = Storage::disk('local')->path('temp');

        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        try {
            $ilovepdf = new Ilovepdf(
                config('services.ilove_pdf.public_key'),
                config('services.ilove_pdf.secret_key')
            );

            $htmlpdfTask = $ilovepdf->newTask('htmlpdf');

            // Add the URL as a cloud file
            $htmlpdfTask->addFile($url, null, null, true);

            // Set output filename
            $htmlpdfTask->setOutputFilename($filename);

            // Execute the task
            $htmlpdfTask->execute();

            // Download the result
            $downloadedFile = $htmlpdfTask->download($tempDir);

            $pdfContent = file_get_contents($downloadedFile);

            // Clean up downloaded file
            if (file_exists($downloadedFile)) {
                unlink($downloadedFile);
            }

            return response($pdfContent, 200, [
                'Content-Type' => 'application/pdf',
                'Content-Disposition' => "attachment; filename=\"{$filename}.pdf\"",
                'Content-Length' => strlen($pdfContent),
            ]);
        } catch (Exception $e) {
            throw new Exception("Failed to convert HTML to PDF: " . $e->getMessage());
        }
    }
}
