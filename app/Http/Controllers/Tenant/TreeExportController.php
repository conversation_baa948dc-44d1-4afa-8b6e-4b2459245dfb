<?php

namespace App\Http\Controllers\Tenant;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\Setting;
use App\Services\CloudflarePdfService;
use Illuminate\Http\Request;

class TreeExportController extends Controller
{
    public function show(Branch $branch)
    {
        // Get the family name for the title
        $familyName = tenant()->getSetting(Setting::FAMILY_NAME) ?? 'شجرة العائلة';

        return inertia('Tenant/Branch/Export', [
            'root' => $branch->getRoot(),
            'branch' => $branch,
            'familyName' => $familyName,
            'settings' => [
                'layout_mode' => 'auto', // Force auto layout for export
            ],
        ]);
    }

    public function exportPdf(Request $request, Branch $branch)
    {
        try {
            // Get the family name for the filename
            $familyName = tenant()->getSetting(Setting::FAMILY_NAME) ?? 'شجرة العائلة';
            $filename = 'tree_export_' . $branch->id . '_' . now()->format('Y_m_d_H_i_s');

            // Generate the tenant URL for the tree export page
            $exportUrl = route('tree.export.show', ['branch' => $branch->id]);

            // Track the export event
            postHog(['event' => 'Tree PDF Export']);

            // Convert HTML to PDF using Cloudflare service
            return CloudflarePdfService::convertUrlToPdf($exportUrl, $filename);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'فشل في تصدير الشجرة كملف PDF. يرجى المحاولة مرة أخرى.',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
